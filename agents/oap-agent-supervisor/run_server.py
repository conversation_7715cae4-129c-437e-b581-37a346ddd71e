#!/usr/bin/env python3
"""
Simple script to run the LangGraph API server for the supervisor agent.
"""
import os
import sys
import uvicorn
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Set environment variables
os.environ.setdefault("LANGGRAPH_CONFIG", "langgraph.json")

def main():
    """Run the LangGraph API server."""
    try:
        # Import the LangGraph API server
        from langgraph_api.server import create_app
        
        # Create the FastAPI app
        app = create_app()
        
        # Run with uvicorn
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=2025,
            log_level="info"
        )
    except ImportError as e:
        print(f"Error importing LangGraph API: {e}")
        print("Trying alternative approach...")
        
        # Alternative: try to run using the CLI module directly
        try:
            from langgraph_cli.cli import cli
            sys.argv = ["langgraph", "dev", "--port", "2025"]
            cli()
        except ImportError as e2:
            print(f"Error importing LangGraph CLI: {e2}")
            print("Please ensure LangGraph is properly installed.")
            sys.exit(1)

if __name__ == "__main__":
    main()
