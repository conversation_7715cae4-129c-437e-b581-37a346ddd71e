from typing import <PERSON><PERSON>, Any

class Comparable:
    def __eq__(self, other: Any) -> bool: ...
    def __ne__(self, other: Any) -> bool: ...
    def __lt__(self, other: Any) -> bool: ...
    def __le__(self, other: Any) -> bool: ...
    def __gt__(self, other: Any) -> bool: ...
    def __ge__(self, other: Any) -> bool: ...
    def _cmp_values(self, other: Any) -> <PERSON><PERSON>[str, str]: ...
